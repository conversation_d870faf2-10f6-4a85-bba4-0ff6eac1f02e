"use client"

import { motion } from "framer-motion"
import { use<PERSON><PERSON><PERSON>, useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import Pagination from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/toast"
import { api } from "@/utils/api"
import { getAuthToken } from "@/utils/auth"

// 支付记录类型定义
type PaymentRecord = {
  tid: number
  transaction_id: string
  request_user: string // 用户姓名
  request_username: string // 用户名
  team_users: string
  method_name: string // 支付方式名称
  amount: string
  region: string
  approved: boolean | null
  t_create_time: string // 提交时间
  evidence: string // minio presigned URL
  transaction_time: string
  review_time: string
  review_user: string
  review_username: string
  reviewer: number
}

// API响应类型定义
type ApiResponse<T> = {
  code: number
  data: T
  msg: string
}

// 详细支付信息类型（用于审阅模态框）
type DetailedPaymentRecord = PaymentRecord

export default function AdminPaymentsPage() {
  const { addToast } = useToast()

  // 状态管理
  const [allPayments, setAllPayments] = useState<PaymentRecord[]>([]) // 所有缓存的数据
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMoreData, setHasMoreData] = useState(true)
  const [selectedPayment, setSelectedPayment] = useState<DetailedPaymentRecord | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [reviewingPayment, setReviewingPayment] = useState<number | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [showEvidenceModal, setShowEvidenceModal] = useState(false)
  const [evidenceImageUrl, setEvidenceImageUrl] = useState<string>("")
  const [loadingEvidence, setLoadingEvidence] = useState(false)

  // 安全的日期格式化函数
  const formatDate = (dateString: string, options: { includeTime?: boolean } = {}) => {
    if (!isClient || !dateString) return ""
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return "Invalid Date"
      return options.includeTime ? date.toLocaleString() : date.toLocaleDateString()
    } catch (_error) {
      return "Invalid Date"
    }
  }

  // 分页配置
  const itemsPerPage = 20
  const fetchLimit = 100

  // 获取支付数据的API函数
  const fetchPayments = useCallback(
    async (offset: number = 0, filter: string = "", status: string = "all") => {
      setLoading(true)
      try {
        // 使用新的API客户端，自动处理认证和错误
        const params = {
          limit: fetchLimit.toString(),
          offset: offset.toString(),
          filter: filter,
          status: status === "all" ? "" : status,
        }

        const result = await api.get<PaymentRecord[]>("/api/admin/payments", params)
        const newPayments = result || []

        if (offset === 0) {
          // 首次加载或重新搜索
          setAllPayments(newPayments)
        } else {
          // 加载更多数据
          setAllPayments((prev) => [...prev, ...newPayments])
        }

        setHasMoreData(newPayments.length === fetchLimit)
      } catch (error) {
        console.error("Error fetching payments:", error)
        addToast({
          type: "error",
          title: "Error",
          message: error instanceof Error ? error.message : "Failed to fetch payment data",
        })
      } finally {
        setLoading(false)
      }
    },
    [addToast, fetchLimit]
  )

  // 初始化数据加载
  useEffect(() => {
    setIsClient(true)
    fetchPayments(0, "", statusFilter)
  }, [fetchPayments, statusFilter])

  // 获取支付状态
  const getPaymentStatus = (payment: PaymentRecord): "pending" | "approved" | "rejected" => {
    if (payment.approved === null) return "pending"
    return payment.approved ? "approved" : "rejected"
  }

  // 过滤和分页逻辑
  const filteredPayments = allPayments.filter((payment) => {
    const userName = `${payment.request_user}(${payment.request_username})`
    const matchesSearch =
      userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.transaction_id.toLowerCase().includes(searchTerm.toLowerCase())
    const paymentStatus = getPaymentStatus(payment)

    // 根据activeTab过滤
    let matchesTab = true
    if (activeTab !== "all") {
      matchesTab = paymentStatus === activeTab
    }

    // 根据statusFilter过滤（用于搜索框旁边的下拉框）
    const matchesStatus = statusFilter === "all" || paymentStatus === statusFilter

    return matchesSearch && matchesTab && matchesStatus
  })

  // 计算当前页显示的数据
  const totalPages = Math.ceil(filteredPayments.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedPayments = filteredPayments.slice(startIndex, endIndex)

  // 处理搜索输入变化（不立即搜索）
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  // 处理回车搜索
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setCurrentPage(1)
      fetchPayments(0, searchTerm, statusFilter)
    }
  }

  // 当状态过滤改变时重置到第一页并重新搜索
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value)
    setCurrentPage(1)
    fetchPayments(0, searchTerm, value)
  }

  // 查看支付凭证 - 通过API获取并在模态框中展示
  const handleViewEvidence = async (paymentId: number) => {
    setLoadingEvidence(true)
    try {
      const token = getAuthToken()
      const response = await fetch(`/api/admin/payments/${paymentId}/evidence`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        throw new Error("Failed to fetch payment evidence")
      }

      const result: ApiResponse<{ evidence: string }> = await response.json()

      if (result.code === 200 && result.data.evidence) {
        setEvidenceImageUrl(result.data.evidence)
        setShowEvidenceModal(true)
      } else {
        addToast({
          title: "Error",
          description: result.msg || "Failed to load payment evidence",
          type: "error",
        })
      }
    } catch (error) {
      console.error("Error fetching payment evidence:", error)
      addToast({
        title: "Error",
        description: "Failed to load payment evidence",
        type: "error",
      })
    } finally {
      setLoadingEvidence(false)
    }
  }

  // 查看详细信息
  const handleViewDetails = async (payment: PaymentRecord) => {
    // 这里可以调用API获取更详细的信息
    setSelectedPayment(payment as DetailedPaymentRecord)
    setShowDetailModal(true)
  }

  // 审批支付
  const handleReviewPayment = async (tid: number, approved: boolean) => {
    setReviewingPayment(tid)
    try {
      // 获取当前用户信息作为reviewer
      const userData = localStorage.getItem("user")
      let reviewerId = 1 // 默认值
      if (userData) {
        try {
          const parsedData = JSON.parse(userData) as Record<string, unknown>
          const userInfo = parsedData.user_info || (parsedData.data as Record<string, unknown>)?.user_info || parsedData
          const userInfoTyped = userInfo as Record<string, unknown>
          reviewerId = (userInfoTyped.id as number) || 1
        } catch (error) {
          console.error("Error parsing user data:", error)
        }
      }

      // 使用新的API客户端，自动处理认证和错误
      await api.post(`/api/admin/payment/review/${tid}`, {
        reviewer: reviewerId,
        approved: approved,
      })

      addToast({
        type: "success",
        title: "Success",
        message: `Payment ${approved ? "approved" : "rejected"} successfully`,
      })

      // 重新获取数据以更新状态
      fetchPayments(0, searchTerm, statusFilter)
    } catch (error) {
      console.error("Error reviewing payment:", error)
      addToast({
        type: "error",
        title: "Error",
        message: error instanceof Error ? error.message : "Failed to review payment",
      })
    } finally {
      setReviewingPayment(null)
    }
  }

  // 加载更多数据（当需要时）
  const loadMoreData = useCallback(() => {
    if (hasMoreData && !loading) {
      fetchPayments(allPayments.length, searchTerm, statusFilter)
    }
  }, [hasMoreData, loading, allPayments.length, searchTerm, statusFilter, fetchPayments])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-amber-50 text-amber-700 border-amber-200"
      case "approved":
        return "bg-green-50 text-green-700 border-green-200"
      case "rejected":
        return "bg-red-50 text-red-700 border-red-200"
      default:
        return "bg-gray-50 text-gray-700 border-gray-200"
    }
  }

  // 统计数据
  const pendingCount = allPayments.filter((p) => getPaymentStatus(p) === "pending").length
  const approvedCount = allPayments.filter((p) => getPaymentStatus(p) === "approved").length
  const rejectedCount = allPayments.filter((p) => getPaymentStatus(p) === "rejected").length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Review</h1>
          <p className="text-gray-600">Review and approve conference registration payments</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Pending Review</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{pendingCount}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
                <i className="fas fa-clock text-xl text-amber-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Approved</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{approvedCount}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-check text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Rejected</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{rejectedCount}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <i className="fas fa-times text-xl text-red-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Records</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{allPayments.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-list text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Payments</TabsTrigger>
          <TabsTrigger value="pending">Pending Review ({pendingCount})</TabsTrigger>
          <TabsTrigger value="approved">Approved ({approvedCount})</TabsTrigger>
          <TabsTrigger value="rejected">Rejected ({rejectedCount})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search by name, username, or transaction ID... (Press Enter to search)"
                    value={searchTerm}
                    onChange={handleSearchInputChange}
                    onKeyPress={handleSearchKeyPress}
                    className="w-full"
                  />
                </div>
                <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Payments List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Payment Records ({filteredPayments.length})</CardTitle>
                {hasMoreData && (
                  <Button variant="outline" onClick={loadMoreData} disabled={loading}>
                    {loading ? "Loading..." : "Load More"}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Transaction ID</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">User</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Team Members</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Method</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Status</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Submitted</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedPayments.map((payment) => (
                      <motion.tr
                        key={payment.tid}
                        className="border-b border-gray-100 hover:bg-gray-50"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <td className="px-4 py-3 font-mono text-sm">{payment.transaction_id}</td>
                        <td className="px-4 py-3">
                          <div className="font-medium text-gray-900">
                            {payment.request_user}({payment.request_username})
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="max-w-32 truncate text-sm text-gray-600" title={payment.team_users}>
                            {payment.team_users || "-"}
                          </div>
                        </td>
                        <td className="px-4 py-3">{payment.method_name}</td>
                        <td className="px-4 py-3">
                          <Badge variant="outline" className={getStatusColor(getPaymentStatus(payment))}>
                            {getPaymentStatus(payment)}
                          </Badge>
                        </td>
                        <td className="px-4 py-3 text-sm">{formatDate(payment.t_create_time)}</td>
                        <td className="px-4 py-3">
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewEvidence(payment.tid)}
                              disabled={loadingEvidence}
                              title="View Receipt"
                            >
                              {loadingEvidence ? (
                                <i className="fas fa-spinner fa-spin text-gray-500"></i>
                              ) : (
                                <i className="fas fa-file-pdf text-red-500"></i>
                              )}
                            </Button>

                            {/* 审批按钮 - 只对pending状态显示 */}
                            {getPaymentStatus(payment) === "pending" && (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleReviewPayment(payment.tid, true)}
                                  disabled={reviewingPayment === payment.tid}
                                  className="bg-green-600 px-2 py-1 text-xs text-white hover:bg-green-700"
                                  title="Approve"
                                >
                                  {reviewingPayment === payment.tid ? (
                                    <i className="fas fa-spinner fa-spin"></i>
                                  ) : (
                                    <i className="fas fa-check"></i>
                                  )}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleReviewPayment(payment.tid, false)}
                                  disabled={reviewingPayment === payment.tid}
                                  className="border-red-300 px-2 py-1 text-xs text-red-700 hover:bg-red-50"
                                  title="Reject"
                                >
                                  {reviewingPayment === payment.tid ? (
                                    <i className="fas fa-spinner fa-spin"></i>
                                  ) : (
                                    <i className="fas fa-times"></i>
                                  )}
                                </Button>
                              </>
                            )}

                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewDetails(payment)}
                              title="View Details"
                            >
                              <i className="fas fa-eye text-gray-500"></i>
                            </Button>
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 分页组件 */}
              {totalPages > 1 && (
                <div className="mt-6 border-t pt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={setCurrentPage}
                    itemsPerPage={itemsPerPage}
                    totalItems={filteredPayments.length}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending">
          {/* 搜索和过滤 */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search pending payments... (Press Enter to search)"
                    value={searchTerm}
                    onChange={handleSearchInputChange}
                    onKeyPress={handleSearchKeyPress}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pending支付列表 */}
          <Card>
            <CardHeader>
              <CardTitle>Pending Payments ({filteredPayments.length})</CardTitle>
              <CardDescription>Payments awaiting review and approval</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredPayments.map((payment) => (
                  <motion.div
                    key={payment.tid}
                    className="rounded-lg border border-amber-200 bg-amber-50 p-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center space-x-2">
                          <h3 className="font-semibold text-gray-900">
                            {payment.request_user}({payment.request_username})
                          </h3>
                          <Badge variant="outline" className="border-amber-300 bg-amber-100 text-amber-800">
                            {payment.method_name}
                          </Badge>
                        </div>
                        <p className="mb-2 text-lg font-semibold text-gray-900">
                          ¥{payment.amount} ({payment.region})
                        </p>
                        <p className="mb-1 text-sm text-gray-500">Transaction ID: {payment.transaction_id}</p>
                        <p className="text-sm text-gray-500">
                          Submitted: {formatDate(payment.t_create_time, { includeTime: true })}
                        </p>
                        {payment.team_users && <p className="text-sm text-gray-500">Team: {payment.team_users}</p>}
                      </div>
                      <div className="ml-4 flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewEvidence(payment.tid)}
                          disabled={loadingEvidence}
                        >
                          {loadingEvidence ? (
                            <i className="fas fa-spinner fa-spin mr-2"></i>
                          ) : (
                            <i className="fas fa-file-pdf mr-2"></i>
                          )}
                          View Receipt
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleReviewPayment(payment.tid, true)}
                          disabled={reviewingPayment === payment.tid}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {reviewingPayment === payment.tid ? (
                            <i className="fas fa-spinner fa-spin mr-2"></i>
                          ) : (
                            <i className="fas fa-check mr-2"></i>
                          )}
                          Approve
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReviewPayment(payment.tid, false)}
                          disabled={reviewingPayment === payment.tid}
                          className="border-red-300 text-red-700 hover:bg-red-50"
                        >
                          {reviewingPayment === payment.tid ? (
                            <i className="fas fa-spinner fa-spin mr-2"></i>
                          ) : (
                            <i className="fas fa-times mr-2"></i>
                          )}
                          Reject
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetails(payment)}>
                          <i className="fas fa-eye mr-2"></i>
                          Details
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}

                {filteredPayments.length === 0 && (
                  <div className="py-8 text-center text-gray-500">No pending payments found.</div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approved">
          {/* 搜索和过滤 */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search approved payments... (Press Enter to search)"
                    value={searchTerm}
                    onChange={handleSearchInputChange}
                    onKeyPress={handleSearchKeyPress}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Approved Payments ({filteredPayments.length})</CardTitle>
              <CardDescription>Successfully approved payments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Transaction ID</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">User</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Amount</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Method</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Approved</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPayments.map((payment) => (
                      <motion.tr
                        key={payment.tid}
                        className="border-b border-gray-100 hover:bg-gray-50"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <td className="px-4 py-3 font-mono text-sm">{payment.transaction_id}</td>
                        <td className="px-4 py-3">
                          <div className="font-medium text-gray-900">
                            {payment.request_user}({payment.request_username})
                          </div>
                        </td>
                        <td className="px-4 py-3 font-semibold">¥{payment.amount}</td>
                        <td className="px-4 py-3">{payment.method_name}</td>
                        <td className="px-4 py-3 text-sm">
                          {payment.review_time ? formatDate(payment.review_time) : "-"}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewEvidence(payment.tid)}
                              disabled={loadingEvidence}
                              title="View Receipt"
                            >
                              {loadingEvidence ? (
                                <i className="fas fa-spinner fa-spin text-gray-500"></i>
                              ) : (
                                <i className="fas fa-file-pdf text-red-500"></i>
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewDetails(payment)}
                              title="View Details"
                            >
                              <i className="fas fa-eye text-gray-500"></i>
                            </Button>
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>

                {filteredPayments.length === 0 && (
                  <div className="py-8 text-center text-gray-500">No approved payments found.</div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rejected">
          {/* 搜索和过滤 */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search rejected payments... (Press Enter to search)"
                    value={searchTerm}
                    onChange={handleSearchInputChange}
                    onKeyPress={handleSearchKeyPress}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rejected Payments ({filteredPayments.length})</CardTitle>
              <CardDescription>Payments that were rejected</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Transaction ID</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">User</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Amount</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Method</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Rejected</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPayments.map((payment) => (
                      <motion.tr
                        key={payment.tid}
                        className="border-b border-gray-100 hover:bg-gray-50"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <td className="px-4 py-3 font-mono text-sm">{payment.transaction_id}</td>
                        <td className="px-4 py-3">
                          <div className="font-medium text-gray-900">
                            {payment.request_user}({payment.request_username})
                          </div>
                        </td>
                        <td className="px-4 py-3 font-semibold">¥{payment.amount}</td>
                        <td className="px-4 py-3">{payment.method_name}</td>
                        <td className="px-4 py-3 text-sm">
                          {payment.review_time ? formatDate(payment.review_time) : "-"}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewEvidence(payment.tid)}
                              disabled={loadingEvidence}
                              title="View Receipt"
                            >
                              {loadingEvidence ? (
                                <i className="fas fa-spinner fa-spin text-gray-500"></i>
                              ) : (
                                <i className="fas fa-file-pdf text-red-500"></i>
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewDetails(payment)}
                              title="View Details"
                            >
                              <i className="fas fa-eye text-gray-500"></i>
                            </Button>
                          </div>
                        </td>
                      </motion.tr>
                    ))}
                  </tbody>
                </table>

                {filteredPayments.length === 0 && (
                  <div className="py-8 text-center text-gray-500">No rejected payments found.</div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 详情模态框 */}
      <Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Payment Details</DialogTitle>
            <DialogDescription>
              Detailed information for transaction ID: {selectedPayment?.transaction_id}
            </DialogDescription>
          </DialogHeader>

          {selectedPayment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Transaction ID</label>
                  <p className="font-mono text-sm">{selectedPayment.transaction_id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">
                    <Badge variant="outline" className={getStatusColor(getPaymentStatus(selectedPayment))}>
                      {getPaymentStatus(selectedPayment)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">User</label>
                  <p>
                    {selectedPayment.request_user}({selectedPayment.request_username})
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Payment Method</label>
                  <p>{selectedPayment.method_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Amount</label>
                  <p className="font-semibold">¥{selectedPayment.amount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Region</label>
                  <p className="capitalize">{selectedPayment.region}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Submitted</label>
                  <p>{formatDate(selectedPayment.t_create_time, { includeTime: true })}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Transaction Time</label>
                  <p>{formatDate(selectedPayment.transaction_time, { includeTime: true })}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Team Members</label>
                  <p>{selectedPayment.team_users || "None"}</p>
                </div>
                {selectedPayment.review_time && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Review Time</label>
                    <p>{formatDate(selectedPayment.review_time, { includeTime: true })}</p>
                  </div>
                )}
                {selectedPayment.review_user && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Reviewed By</label>
                    <p>
                      {selectedPayment.review_user}({selectedPayment.review_username})
                    </p>
                  </div>
                )}
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Payment Evidence</label>
                <div className="mt-2">
                  <Button
                    variant="outline"
                    onClick={() => handleViewEvidence(selectedPayment.tid)}
                    disabled={loadingEvidence}
                  >
                    {loadingEvidence ? (
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                    ) : (
                      <i className="fas fa-file-pdf mr-2"></i>
                    )}
                    View Receipt
                  </Button>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 支付凭证图片展示模态框 */}
      <Dialog open={showEvidenceModal} onOpenChange={setShowEvidenceModal}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Payment Receipt</DialogTitle>
            <DialogDescription>
              Payment evidence image
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-center">
            {evidenceImageUrl && (
              <div className="relative max-h-[70vh] max-w-full overflow-auto">
                <Image
                  src={evidenceImageUrl}
                  alt="Payment Receipt"
                  width={800}
                  height={600}
                  className="h-auto max-w-full rounded-lg object-contain"
                  unoptimized
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
